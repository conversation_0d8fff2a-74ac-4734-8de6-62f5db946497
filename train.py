import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
from loss_functions import AEAlignedLoss, AdaptiveLoss, compute_ae_metric
import argparse
from tqdm import tqdm
import math
import copy

# ----------------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------------

def read_cfg_file(file_path: str):
    """Parse the cfg txt to obtain antenna dims, IQ channels and rank.
    Returns: (samp_num, M, N, IQ, R)
    """
    with open(file_path, "r") as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    samp_num = int(lines[0])
    M = int(lines[1])
    N = int(lines[2])
    IQ = int(lines[3])
    R = int(lines[4])
    return samp_num, M, N, IQ, R


# ----------------------------------------------------------------------------
# Snapshot Ensemble Implementation
# ----------------------------------------------------------------------------

class CyclicCosineAnnealingLR:
    """Cyclic Cosine Annealing Learning Rate Scheduler for Snapshot Ensembles."""

    def __init__(self, optimizer, T_max, eta_min=0, cycles=5):
        self.optimizer = optimizer
        self.T_max = T_max  # Total epochs
        self.eta_min = eta_min
        self.cycles = cycles
        self.cycle_length = T_max // cycles
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        self.current_cycle = 0
        self.cycle_epoch = 0

    def step(self, epoch):
        """Update learning rate based on current epoch."""
        self.current_cycle = epoch // self.cycle_length
        self.cycle_epoch = epoch % self.cycle_length

        # Cosine annealing within each cycle
        for param_group, base_lr in zip(self.optimizer.param_groups, self.base_lrs):
            param_group['lr'] = self.eta_min + (base_lr - self.eta_min) * \
                (1 + math.cos(math.pi * self.cycle_epoch / self.cycle_length)) / 2

    def is_snapshot_epoch(self, epoch):
        """Check if current epoch is a snapshot epoch (end of cycle)."""
        return (epoch + 1) % self.cycle_length == 0 and epoch > 0

    def get_last_lr(self):
        """Get current learning rates."""
        return [group['lr'] for group in self.optimizer.param_groups]


class SnapshotEnsemble:
    """
    Enhanced Snapshot Ensemble manager for teacher model generation.

    Implements the three-stage teacher-student framework as described in Section 4:
    - Quality-based snapshot selection with diversity consideration
    - Adaptive snapshot management based on performance metrics
    - Support for knowledge distillation preparation
    """

    def __init__(self, save_dir="snapshots", max_snapshots=5,
                 min_quality_threshold=0.1, diversity_weight=0.3):
        self.save_dir = save_dir
        self.max_snapshots = max_snapshots
        self.min_quality_threshold = min_quality_threshold
        self.diversity_weight = diversity_weight
        self.snapshots = []
        self.best_snapshot_quality = float('inf')

        # Create save directory
        os.makedirs(save_dir, exist_ok=True)

        print(f"[Enhanced SnapshotEnsemble] Max snapshots: {max_snapshots}, "
              f"Quality threshold: {min_quality_threshold}, Diversity weight: {diversity_weight}")

    def save_snapshot(self, model, epoch, loss, ae_metric=None, additional_metrics=None):
        """
        Save a model snapshot with enhanced quality assessment.

        Args:
            model: Model to save
            epoch: Current epoch
            loss: Training loss
            ae_metric: AE metric value for quality assessment
            additional_metrics: Dict of additional metrics for diversity assessment
        """
        # Quality assessment
        quality_score = ae_metric if ae_metric is not None else loss

        # Only save if quality meets threshold
        if quality_score > self.min_quality_threshold and len(self.snapshots) >= self.max_snapshots:
            print(f"[SnapshotEnsemble] Skipping snapshot at epoch {epoch}: "
                  f"quality {quality_score:.6f} below threshold {self.min_quality_threshold}")
            return False

        snapshot_path = os.path.join(self.save_dir, f"snapshot_epoch_{epoch}_ae_{quality_score:.6f}.pth")

        # Enhanced snapshot metadata
        snapshot_data = {
            'epoch': epoch,
            'model_state_dict': copy.deepcopy(model.state_dict()),
            'loss': loss,
            'ae_metric': ae_metric,
            'quality_score': quality_score,
            'additional_metrics': additional_metrics or {}
        }

        torch.save(snapshot_data, snapshot_path)

        snapshot_info = {
            'path': snapshot_path,
            'epoch': epoch,
            'loss': loss,
            'ae_metric': ae_metric,
            'quality_score': quality_score,
            'additional_metrics': additional_metrics or {}
        }

        self.snapshots.append(snapshot_info)
        self.best_snapshot_quality = min(self.best_snapshot_quality, quality_score)

        # Enhanced snapshot management with diversity consideration
        if len(self.snapshots) > self.max_snapshots:
            self._manage_snapshots_with_diversity()

        print(f"[SnapshotEnsemble] Saved snapshot at epoch {epoch}, "
              f"quality: {quality_score:.6f}, total snapshots: {len(self.snapshots)}")
        return True

    def _manage_snapshots_with_diversity(self):
        """Manage snapshots considering both quality and diversity."""
        if len(self.snapshots) <= self.max_snapshots:
            return

        # Calculate diversity scores between snapshots
        diversity_scores = []
        for i, snapshot in enumerate(self.snapshots):
            diversity_score = 0.0
            for j, other_snapshot in enumerate(self.snapshots):
                if i != j:
                    # Simple diversity metric based on loss difference
                    diversity_score += abs(snapshot['loss'] - other_snapshot['loss'])
            diversity_scores.append(diversity_score / (len(self.snapshots) - 1))

        # Combined score: quality + diversity
        combined_scores = []
        for i, snapshot in enumerate(self.snapshots):
            quality_component = 1.0 / (1.0 + snapshot['quality_score'])  # Lower is better
            diversity_component = diversity_scores[i]
            combined_score = (1 - self.diversity_weight) * quality_component + \
                           self.diversity_weight * diversity_component
            combined_scores.append((combined_score, i))

        # Sort by combined score (higher is better) and keep the best
        combined_scores.sort(reverse=True)
        indices_to_keep = [idx for _, idx in combined_scores[:self.max_snapshots]]

        # Remove worst snapshots
        snapshots_to_remove = [self.snapshots[i] for i in range(len(self.snapshots))
                              if i not in indices_to_keep]

        for snapshot in snapshots_to_remove:
            if os.path.exists(snapshot['path']):
                os.remove(snapshot['path'])

        # Keep only selected snapshots
        self.snapshots = [self.snapshots[i] for i in indices_to_keep]

    def load_snapshots(self):
        """Load all saved snapshots."""
        snapshot_files = glob.glob(os.path.join(self.save_dir, "snapshot_*.pth"))
        loaded_snapshots = []

        for snapshot_file in snapshot_files:
            snapshot_data = torch.load(snapshot_file, map_location='cpu')
            loaded_snapshots.append({
                'path': snapshot_file,
                'state_dict': snapshot_data['model_state_dict'],
                'epoch': snapshot_data['epoch'],
                'loss': snapshot_data['loss']
            })

        return loaded_snapshots

    def get_teacher_ensemble(self):
        """Get list of teacher models for knowledge distillation."""
        teacher_models = []
        for snapshot in self.snapshots:
            # Load model state
            checkpoint = torch.load(snapshot['path'], map_location='cpu')
            teacher_models.append({
                'state_dict': checkpoint['model_state_dict'],
                'quality_score': checkpoint.get('quality_score', checkpoint['loss']),
                'epoch': checkpoint['epoch']
            })
        return teacher_models


class KnowledgeDistillationTrainer:
    """
    Knowledge Distillation trainer for Stage 2 of the teacher-student framework.

    Implements knowledge transfer from teacher ensemble to lightweight student model
    as described in Section 4.2.
    """

    def __init__(self, teacher_ensemble, student_model, temperature=4.0,
                 alpha=0.7, beta=0.3):
        """
        Args:
            teacher_ensemble: List of teacher model state dicts
            student_model: Student model to train
            temperature: Temperature for softmax in knowledge distillation
            alpha: Weight for distillation loss
            beta: Weight for student task loss
        """
        self.teacher_models = []
        self.student_model = student_model
        self.temperature = temperature
        self.alpha = alpha
        self.beta = beta

        # Load teacher models
        for teacher_info in teacher_ensemble:
            teacher_model = SVDNet(M=64, N=64, R=16)  # Same architecture as student
            teacher_model.load_state_dict(teacher_info['state_dict'])
            teacher_model.eval()
            self.teacher_models.append(teacher_model)

        print(f"[KnowledgeDistillation] Loaded {len(self.teacher_models)} teacher models, "
              f"T={temperature}, α={alpha}, β={beta}")

    def compute_distillation_loss(self, student_outputs, teacher_outputs, targets):
        """
        Compute knowledge distillation loss combining teacher knowledge and task loss.

        Args:
            student_outputs: (U_s, S_s, V_s) from student model
            teacher_outputs: List of (U_t, S_t, V_t) from teacher models
            targets: Ground truth channel matrices

        Returns:
            distillation_loss: Combined distillation loss
        """
        U_s, S_s, V_s = student_outputs

        # Ensemble teacher predictions (average)
        teacher_U_avg = torch.stack([outputs[0] for outputs in teacher_outputs]).mean(0)
        teacher_S_avg = torch.stack([outputs[1] for outputs in teacher_outputs]).mean(0)
        teacher_V_avg = torch.stack([outputs[2] for outputs in teacher_outputs]).mean(0)

        # Knowledge distillation loss (MSE between student and teacher features)
        kd_loss_U = torch.nn.functional.mse_loss(U_s, teacher_U_avg.detach())
        kd_loss_S = torch.nn.functional.mse_loss(S_s, teacher_S_avg.detach())
        kd_loss_V = torch.nn.functional.mse_loss(V_s, teacher_V_avg.detach())

        kd_loss = (kd_loss_U + kd_loss_S + kd_loss_V) / 3.0

        # Task loss (standard AE-aligned loss)
        task_loss_fn = AEAlignedLoss(geometric_emphasis=True)
        task_loss_dict = task_loss_fn(U_s, S_s, V_s, targets)
        task_loss = task_loss_dict['total_loss']

        # Combined loss
        total_loss = self.alpha * kd_loss + self.beta * task_loss

        return {
            'total_loss': total_loss,
            'kd_loss': kd_loss,
            'task_loss': task_loss,
            'kd_loss_U': kd_loss_U,
            'kd_loss_S': kd_loss_S,
            'kd_loss_V': kd_loss_V
        }


# ----------------------------------------------------------------------------
# Dataset definition (memory-mapped to keep RAM usage low)
# ----------------------------------------------------------------------------

class ChannelDataset(Dataset):
    def __init__(self, data_files, label_files):
        assert len(data_files) == len(label_files), "data/label file count mismatch"
        self.data_arrays = [np.load(f, mmap_mode="r") for f in data_files]
        self.label_arrays = [np.load(f, mmap_mode="r") for f in label_files]
        self.cumsum = np.cumsum([arr.shape[0] for arr in self.data_arrays])

    def __len__(self):
        return int(self.cumsum[-1])

    def __getitem__(self, idx):
        file_idx = int(np.searchsorted(self.cumsum, idx, side="right"))
        prev_cum = 0 if file_idx == 0 else self.cumsum[file_idx - 1]
        inner_idx = idx - prev_cum
        H_in = self.data_arrays[file_idx][inner_idx]     # shape [M, N, 2]
        H_gt = self.label_arrays[file_idx][inner_idx]    # ideal channel
        # numpy -> torch
        H_in = torch.from_numpy(H_in).float()
        H_gt = torch.from_numpy(H_gt).float()
        return H_in, H_gt

# ----------------------------------------------------------------------------
# Complex helpers
# ----------------------------------------------------------------------------

def to_complex(t):
    return torch.complex(t[..., 0], t[..., 1])

# ----------------------------------------------------------------------------
# Training loop
# ----------------------------------------------------------------------------

def frob_norm(t):
    """Compute Frobenius norm squared for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))  # returns [...]


def apply_data_augmentation(H_in, H_gt):
    """Apply data augmentation strategies as described in the solution document.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)

    # 1. Additional noise injection
    noise_std = 0.01  # Small additional noise
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation
    batch_size = H_in_c.shape[0]
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def apply_enhanced_data_augmentation(H_in, H_gt):
    """Enhanced data augmentation with more sophisticated techniques.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)
    batch_size = H_in_c.shape[0]

    # 1. Adaptive noise injection based on signal strength
    signal_power = torch.mean(torch.abs(H_in_c) ** 2, dim=(-2, -1), keepdim=True)
    noise_std = 0.005 + 0.01 * torch.sqrt(signal_power)  # Adaptive noise level
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation (global)
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # 3. Random conjugate transpose (with probability 0.5)
    if torch.rand(1).item() > 0.5:
        H_in_c = torch.conj(H_in_c.transpose(-2, -1))

    # 4. Small random scaling
    scale_factor = 1.0 + 0.05 * (torch.rand(batch_size, 1, 1, device=H_in_c.device) - 0.5)
    H_in_c = H_in_c * scale_factor

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def apply_manifold_aware_augmentation(H_in, H_gt, augmentation_strength=0.1):
    """
    Manifold-aware data augmentation as described in the optimization document.

    Implements advanced augmentation techniques that respect the geometric structure
    of the channel matrix manifold:
    1. Adaptive noise injection based on local manifold curvature
    2. Phase rotation with manifold-preserving constraints
    3. Singular value perturbation in the SVD domain
    4. Structured noise injection that preserves rank properties

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]
        augmentation_strength: Control parameter for augmentation intensity

    Returns:
        H_in_aug: Manifold-aware augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)
    H_gt_c = to_complex(H_gt)
    batch_size, M, N = H_in_c.shape

    # 1. Manifold-aware adaptive noise injection
    # Estimate local manifold curvature using SVD
    try:
        U_gt, S_gt, Vh_gt = torch.linalg.svd(H_gt_c, full_matrices=False)

        # Compute condition number as a proxy for manifold curvature
        condition_numbers = S_gt[..., 0] / (S_gt[..., -1] + 1e-8)

        # Adaptive noise level based on condition number
        # Higher condition number -> more careful noise injection
        adaptive_noise_std = augmentation_strength * 0.01 / (1.0 + 0.1 * condition_numbers.unsqueeze(-1).unsqueeze(-1))

        # Generate structured noise that respects the SVD structure
        noise_U = torch.randn_like(U_gt) * adaptive_noise_std.unsqueeze(-1)
        noise_V = torch.randn_like(Vh_gt) * adaptive_noise_std.unsqueeze(-1)

        # Apply noise in the SVD domain to preserve structure
        U_noisy = U_gt + noise_U
        V_noisy = Vh_gt + noise_V

        # Re-orthogonalize to maintain unitary properties
        U_noisy, _ = torch.linalg.qr(U_noisy)
        V_noisy, _ = torch.linalg.qr(V_noisy.transpose(-2, -1))
        V_noisy = V_noisy.transpose(-2, -1)

        # Reconstruct with structured noise
        H_structured = torch.matmul(U_noisy, torch.matmul(torch.diag_embed(S_gt), V_noisy))

        # Blend with original input
        blend_factor = 0.3
        H_in_c = (1 - blend_factor) * H_in_c + blend_factor * H_structured

    except RuntimeError:
        # Fallback to standard noise injection if SVD fails
        signal_power = torch.mean(torch.abs(H_in_c) ** 2, dim=(-2, -1), keepdim=True)
        noise_std = augmentation_strength * 0.01 * torch.sqrt(signal_power)
        noise = torch.randn_like(H_in_c) * noise_std
        H_in_c = H_in_c + noise

    # 2. Phase rotation with manifold constraints
    # Apply different phase rotations to different singular value components
    try:
        U_in, S_in, Vh_in = torch.linalg.svd(H_in_c, full_matrices=False)

        # Generate phase rotations for each singular value
        rank = min(M, N)
        phase_rotations = torch.exp(1j * torch.rand(batch_size, rank, device=H_in_c.device) * 2 * torch.pi)

        # Apply phase rotations to singular values
        S_rotated = S_in * phase_rotations

        # Reconstruct with phase-rotated singular values
        H_in_c = torch.matmul(U_in, torch.matmul(torch.diag_embed(S_rotated), Vh_in))

    except RuntimeError:
        # Fallback to global phase rotation
        theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
        phase_rotation = torch.exp(1j * theta)
        H_in_c = H_in_c * phase_rotation

    # 3. Rank-preserving perturbations
    # Add small perturbations that don't significantly change the rank structure
    if torch.rand(1).item() > 0.5:
        # Low-rank perturbation
        perturbation_rank = max(1, min(4, min(M, N) // 4))
        U_pert = torch.randn(batch_size, M, perturbation_rank, dtype=H_in_c.dtype, device=H_in_c.device)
        V_pert = torch.randn(batch_size, perturbation_rank, N, dtype=H_in_c.dtype, device=H_in_c.device)
        S_pert = torch.rand(batch_size, perturbation_rank, device=H_in_c.device) * augmentation_strength * 0.1

        low_rank_perturbation = torch.matmul(U_pert, torch.matmul(torch.diag_embed(S_pert), V_pert))
        H_in_c = H_in_c + low_rank_perturbation

    # 4. Geometric transformations that preserve essential properties
    if torch.rand(1).item() > 0.7:
        # Random unitary transformation (preserves spectral properties)
        Q1 = torch.randn(batch_size, M, M, dtype=H_in_c.dtype, device=H_in_c.device)
        Q2 = torch.randn(batch_size, N, N, dtype=H_in_c.dtype, device=H_in_c.device)

        # Make unitary via QR decomposition
        Q1, _ = torch.linalg.qr(Q1)
        Q2, _ = torch.linalg.qr(Q2)

        # Apply weak unitary transformation
        transformation_strength = augmentation_strength * 0.1
        I_M = torch.eye(M, dtype=H_in_c.dtype, device=H_in_c.device).expand(batch_size, -1, -1)
        I_N = torch.eye(N, dtype=H_in_c.dtype, device=H_in_c.device).expand(batch_size, -1, -1)

        Q1_weak = (1 - transformation_strength) * I_M + transformation_strength * Q1
        Q2_weak = (1 - transformation_strength) * I_N + transformation_strength * Q2

        H_in_c = torch.matmul(Q1_weak, torch.matmul(H_in_c, Q2_weak.transpose(-2, -1).conj()))

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def train_with_snapshots(model, dataloader, device="cpu", lr=1e-3, epochs=200, weight_decay=1e-4,
                        cycles=5, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1,
                        use_adaptive_loss=True):
    """Enhanced training function with snapshot ensembles and adaptive loss.

    Key enhancements:
    - Snapshot ensemble with cyclic cosine annealing
    - Adaptive multi-component loss function aligned with AE metric
    - Enhanced data augmentation
    - Numerical stability improvements
    """
    model.to(device)

    # AdamW optimizer as recommended in the document
    opt = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)

    # Cyclic cosine annealing scheduler for snapshot ensembles
    scheduler = CyclicCosineAnnealingLR(opt, T_max=epochs, cycles=cycles)

    # Snapshot ensemble manager
    snapshot_ensemble = SnapshotEnsemble(save_dir="snapshots", max_snapshots=cycles)

    # Enhanced loss function with adaptive capability
    if use_adaptive_loss:
        loss_fn = AdaptiveLoss(
            initial_lambda_rec=lambda_rec,
            initial_lambda_orth_u=lambda_orth_u,
            initial_lambda_orth_v=lambda_orth_v,
            adaptation_rate=0.01
        ).to(device)
        print("[Training] Using adaptive loss function with learnable orthogonality weights")
    else:
        loss_fn = AEAlignedLoss(
            lambda_rec=lambda_rec,
            lambda_orth_u=lambda_orth_u,
            lambda_orth_v=lambda_orth_v
        ).to(device)
        print("[Training] Using fixed-weight AE-aligned loss function")

    for ep in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_orth_loss = 0.0

        prog_bar = tqdm(dataloader, desc=f"Epoch {ep}/{epochs} (Cycle {ep//scheduler.cycle_length + 1})")

        for H_in, H_gt in prog_bar:
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)

            # Apply enhanced data augmentation
            H_in_aug, H_gt = apply_enhanced_data_augmentation(H_in, H_gt)

            # Forward pass
            U, S, V = model(H_in_aug)

            # Ensure batch dim
            if U.ndim == 3:  # squeezed when batch==1
                U = U.unsqueeze(0)
                V = V.unsqueeze(0)
                S = S.unsqueeze(0)

            # ----- Enhanced Multi-component loss function (aligned with AE metric) -----
            loss_dict = loss_fn(U, S, V, H_gt)
            total_loss_batch = loss_dict['total_loss']
            recon_loss = loss_dict['reconstruction_loss']
            orth_U = loss_dict['orthogonality_loss_u']
            orth_V = loss_dict['orthogonality_loss_v']

            # Compute AE metric for monitoring
            with torch.no_grad():
                ae_metric = compute_ae_metric(U, S, V, H_gt)

            # Optimization step
            opt.zero_grad()
            total_loss_batch.backward()

            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            opt.step()

            # Accumulate losses for logging
            total_loss += total_loss_batch.item() * H_in.size(0)
            total_recon_loss += recon_loss.item() * H_in.size(0)
            total_orth_loss += (orth_U.item() + orth_V.item()) * H_in.size(0)

            prog_bar.set_postfix({
                "loss": f"{total_loss_batch.item():.4f}",
                "recon": f"{recon_loss.item():.4f}",
                "orth": f"{(orth_U.item() + orth_V.item()):.2e}",
                "ae": f"{ae_metric.item():.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}"
            })

        # Update learning rate
        scheduler.step(ep - 1)

        # Compute average losses
        avg_loss = total_loss / len(dataloader.dataset)
        avg_recon_loss = total_recon_loss / len(dataloader.dataset)
        avg_orth_loss = total_orth_loss / len(dataloader.dataset)

        print(f"[Summary] Epoch {ep}/{epochs} | "
              f"Loss={avg_loss:.6f} | "
              f"Recon={avg_recon_loss:.6f} | "
              f"Orth={avg_orth_loss:.2e} | "
              f"LR={scheduler.get_last_lr()[0]:.2e}")

        # Save snapshot at the end of each cycle
        if scheduler.is_snapshot_epoch(ep - 1):
            snapshot_ensemble.save_snapshot(model, ep, avg_loss)
            print(f"Snapshot saved at epoch {ep} (end of cycle {scheduler.current_cycle + 1})")

    return model, snapshot_ensemble

# ----------------------------------------------------------------------------
# Main entry
# ----------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train Ortho-Efficient SVDNet on provided channel data")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1", help="Directory containing Round1TrainData*.npy")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Training device (cuda/cpu)")
    parser.add_argument("--batch_size", type=int, default=128, help="Batch size (64/128 recommended)")
    parser.add_argument("--epochs", type=int, default=200, help="Training epochs (200-300 recommended)")
    parser.add_argument("--lr", type=float, default=1e-3, help="Initial learning rate")
    parser.add_argument("--weight_decay", type=float, default=1e-4, help="Weight decay for regularization")
    parser.add_argument("--num_neg_ones_in_D", type=int, default=0,
                       help="Number of -1 elements in D matrix for Cayley transform")
    parser.add_argument("--learnable_D", action="store_true",
                       help="Make D matrix learnable in Cayley transform")
    parser.add_argument("--use_adaptive_loss", action="store_true", default=True,
                       help="Use adaptive loss function with learnable weights")
    args = parser.parse_args()

    # Detect training files (Round1)
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")  # assume same dims for all
    if not data_files or not label_files:
        raise FileNotFoundError("Training *.npy files not found under given data_dir")

    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Found {len(data_files)} training files, each dim=({M},{N}), R={R}, device={args.device}")
    print(f"Training Ortho-Efficient SVDNet with Scaled Cayley Transform for hard orthogonality constraints")

    ds = ChannelDataset(data_files, label_files)
    loader = DataLoader(ds, batch_size=args.batch_size, shuffle=True, num_workers=0)

    # Initialize the enhanced SVDNet with configurable D matrix
    model = SVDNet(
        dim=M,
        rank=R,
        weight_path="",
        num_neg_ones_in_D=args.num_neg_ones_in_D,
        learnable_D=args.learnable_D
    )
    print(f"Enhanced model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"D matrix configuration: num_neg_ones={args.num_neg_ones_in_D}, learnable={args.learnable_D}")

    # Train the model with enhanced snapshot ensembles and adaptive loss
    trained_model, snapshot_ensemble = train_with_snapshots(
        model, loader,
        device=args.device,
        lr=args.lr,
        epochs=args.epochs,
        weight_decay=args.weight_decay,
        cycles=10,  # Number of snapshot cycles
        lambda_rec=1.0,      # Initial reconstruction loss weight
        lambda_orth_u=0.1,   # Initial U orthogonality loss weight
        lambda_orth_v=0.1,   # Initial V orthogonality loss weight
        use_adaptive_loss=args.use_adaptive_loss  # Configurable adaptive loss weights
    )

    # Save final trained weights
    torch.save(trained_model.state_dict(), "svdnet_final.pth")
    print("Final weights saved to svdnet_final.pth")

    # Print snapshot information
    print(f"\nSnapshot Ensemble Summary:")
    print(f"Total snapshots saved: {len(snapshot_ensemble.snapshots)}")
    for i, snapshot in enumerate(snapshot_ensemble.snapshots):
        print(f"  Snapshot {i+1}: Epoch {snapshot['epoch']}, Loss {snapshot['loss']:.6f}")

    print("Training with snapshot ensembles completed successfully!")