import os
import torch
import torch.nn as nn
import torch.nn.functional as F

# -----------------------------------------------------------------------------
# ConvNeXt Block Implementation
# -----------------------------------------------------------------------------
class ConvNeXtBlock(nn.Module):
    """ConvNeXt Block with depthwise convolution, layer normalization, and inverted bottleneck."""

    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)  # depthwise conv
        self.norm = nn.LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)  # pointwise/1x1 convs, implemented with linear layers
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim)),
                                requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # work with diff dim tensors, not just 2D ConvNets
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output


# -----------------------------------------------------------------------------
# ConvNeXt Encoder Implementation
# -----------------------------------------------------------------------------
class ConvNeXtEncoder(nn.Module):
    """Lightweight ConvNeXt encoder following ConvNeXt-T architecture."""

    def __init__(self, in_chans=2, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., layer_scale_init_value=1e-6):
        super().__init__()

        self.downsample_layers = nn.ModuleList()  # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            nn.LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)

        for i in range(3):
            downsample_layer = nn.Sequential(
                nn.LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList()  # 4 feature resolution stages, each consisting of multiple residual blocks
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[ConvNeXtBlock(dim=dims[i], drop_path=dp_rates[cur + j],
                layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

    def forward(self, x):
        features = []
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
            features.append(x)
        return features


# Custom LayerNorm for ConvNeXt
class LayerNorm(nn.Module):
    """LayerNorm that supports two data formats: channels_last (default) or channels_first."""

    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape, )

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x


# Patch LayerNorm to use custom implementation
nn.LayerNorm = LayerNorm


# -----------------------------------------------------------------------------
# Swin Transformer Implementation for CS-UNet Bottleneck
# -----------------------------------------------------------------------------

def window_partition(x, window_size):
    """
    Args:
        x: (B, H, W, C)
        window_size (int): window size

    Returns:
        windows: (num_windows*B, window_size, window_size, C)
    """
    B, H, W, C = x.shape
    x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """
    Args:
        windows: (num_windows*B, window_size, window_size, C)
        window_size (int): Window size
        H (int): Height of image
        W (int): Width of image

    Returns:
        x: (B, H, W, C)
    """
    B = int(windows.shape[0] / (H * W / window_size / window_size))
    x = windows.view(B, H // window_size, W // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return x


class WindowAttention(nn.Module):
    """Window based multi-head self attention (W-MSA) module with relative position bias."""

    def __init__(self, dim, window_size, num_heads, qkv_bias=True, qk_scale=None, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size  # Wh, Ww
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        # define a parameter table of relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))  # 2*Wh-1 * 2*Ww-1, nH

        # get pair-wise relative position index for each token inside the window
        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w]))  # 2, Wh, Ww
        coords_flatten = torch.flatten(coords, 1)  # 2, Wh*Ww
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]  # 2, Wh*Ww, Wh*Ww
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()  # Wh*Ww, Wh*Ww, 2
        relative_coords[:, :, 0] += self.window_size[0] - 1  # shift to start from 0
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)  # Wh*Ww, Wh*Ww
        self.register_buffer("relative_position_index", relative_position_index)

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, mask=None):
        """
        Args:
            x: input features with shape of (num_windows*B, N, C)
            mask: (0/-inf) mask with shape of (num_windows, Wh*Ww, Wh*Ww) or None
        """
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # make torchscript happy (cannot use tensor as tuple)

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)  # Wh*Ww,Wh*Ww,nH
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()  # nH, Wh*Ww, Wh*Ww
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = self.softmax(attn)
        else:
            attn = self.softmax(attn)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer Block."""

    def __init__(self, dim, input_resolution, num_heads, window_size=7, shift_size=0,
                 mlp_ratio=4., qkv_bias=True, qk_scale=None, drop=0., attn_drop=0., drop_path=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.input_resolution = input_resolution
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        if min(self.input_resolution) <= self.window_size:
            # if window size is larger than input resolution, we don't partition windows
            self.shift_size = 0
            self.window_size = min(self.input_resolution)
        assert 0 <= self.shift_size < self.window_size, "shift_size must in 0-window_size"

        self.norm1 = norm_layer(dim)
        self.attn = WindowAttention(
            dim, window_size=(self.window_size, self.window_size), num_heads=num_heads,
            qkv_bias=qkv_bias, qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

        if self.shift_size > 0:
            # calculate attention mask for SW-MSA
            H, W = self.input_resolution
            img_mask = torch.zeros((1, H, W, 1))  # 1 H W 1
            h_slices = (slice(0, -self.window_size),
                        slice(-self.window_size, -self.shift_size),
                        slice(-self.shift_size, None))
            w_slices = (slice(0, -self.window_size),
                        slice(-self.window_size, -self.shift_size),
                        slice(-self.shift_size, None))
            cnt = 0
            for h in h_slices:
                for w in w_slices:
                    img_mask[:, h, w, :] = cnt
                    cnt += 1

            mask_windows = window_partition(img_mask, self.window_size)  # nW, window_size, window_size, 1
            mask_windows = mask_windows.view(-1, self.window_size * self.window_size)
            attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
            attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))
        else:
            attn_mask = None

        self.register_buffer("attn_mask", attn_mask)

    def forward(self, x):
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"

        shortcut = x
        x = self.norm1(x)
        x = x.view(B, H, W, C)

        # cyclic shift
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # partition windows
        x_windows = window_partition(shifted_x, self.window_size)  # nW*B, window_size, window_size, C
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)  # nW*B, window_size*window_size, C

        # W-MSA/SW-MSA
        attn_windows = self.attn(x_windows, mask=self.attn_mask)  # nW*B, window_size*window_size, C

        # merge windows
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)  # B H' W' C

        # reverse cyclic shift
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x
        x = x.view(B, H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class Mlp(nn.Module):
    """Multilayer perceptron."""

    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class SwinBottleneck(nn.Module):
    """Swin Transformer bottleneck for CS-UNet architecture."""

    def __init__(self, dim, input_resolution, depth=2, num_heads=8, window_size=7,
                 mlp_ratio=4., qkv_bias=True, qk_scale=None, drop_rate=0., attn_drop_rate=0.,
                 drop_path_rate=0.1, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.input_resolution = input_resolution
        self.depth = depth

        # build blocks
        self.blocks = nn.ModuleList([
            SwinTransformerBlock(dim=dim, input_resolution=input_resolution,
                                 num_heads=num_heads, window_size=window_size,
                                 shift_size=0 if (i % 2 == 0) else window_size // 2,
                                 mlp_ratio=mlp_ratio,
                                 qkv_bias=qkv_bias, qk_scale=qk_scale,
                                 drop=drop_rate, attn_drop=attn_drop_rate,
                                 drop_path=drop_path_rate,
                                 norm_layer=norm_layer)
            for i in range(depth)])

    def forward(self, x):
        """
        Args:
            x: Input feature tensor (B, C, H, W)
        Returns:
            Output feature tensor (B, C, H, W)
        """
        B, C, H, W = x.shape

        # Convert from (B, C, H, W) to (B, H*W, C) for Swin Transformer
        x = x.flatten(2).transpose(1, 2)  # B, H*W, C

        # Apply Swin Transformer blocks
        for blk in self.blocks:
            x = blk(x)

        # Convert back to (B, C, H, W)
        x = x.transpose(1, 2).view(B, C, H, W)

        return x


# -----------------------------------------------------------------------------
# Enhanced ConvNeXt Encoder with Swin Bottleneck (CS-UNet)
# -----------------------------------------------------------------------------
class CSUNetEncoder(nn.Module):
    """ConvNeXt-Swin-UNet encoder following the hybrid architecture design."""

    def __init__(self, in_chans=2, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., layer_scale_init_value=1e-6,
                 swin_num_heads=24, swin_window_size=7, swin_depth=2):
        super().__init__()

        self.downsample_layers = nn.ModuleList()  # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            nn.LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)

        for i in range(3):
            downsample_layer = nn.Sequential(
                nn.LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        # ConvNeXt stages for the first 3 levels (high and medium resolution)
        self.convnext_stages = nn.ModuleList()
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths[:3]))]
        cur = 0
        for i in range(3):  # Only first 3 stages use ConvNeXt
            stage = nn.Sequential(
                *[ConvNeXtBlock(dim=dims[i], drop_path=dp_rates[cur + j],
                layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.convnext_stages.append(stage)
            cur += depths[i]

        # Swin Transformer bottleneck for the 4th level (lowest resolution)
        # Calculate input resolution for the bottleneck (assuming 64x64 input)
        bottleneck_resolution = (64 // 16, 64 // 16)  # After 4 downsampling operations: 4x4
        self.swin_bottleneck = SwinBottleneck(
            dim=dims[3],  # 768
            input_resolution=bottleneck_resolution,
            depth=swin_depth,
            num_heads=swin_num_heads,
            window_size=min(swin_window_size, min(bottleneck_resolution)),
            mlp_ratio=4.,
            qkv_bias=True,
            drop_rate=0.,
            attn_drop_rate=0.,
            drop_path_rate=0.1
        )

    def forward(self, x):
        features = []

        # Process through ConvNeXt stages (first 3 levels)
        for i in range(3):
            x = self.downsample_layers[i](x)
            x = self.convnext_stages[i](x)
            features.append(x)

        # Final downsampling to bottleneck
        x = self.downsample_layers[3](x)

        # Process through Swin Transformer bottleneck
        x = self.swin_bottleneck(x)
        features.append(x)

        return features


# -----------------------------------------------------------------------------
# ConvNeXt-UNet Decoder Implementation
# -----------------------------------------------------------------------------
class ConvNeXtUNetDecoder(nn.Module):
    """ConvNeXt-UNet decoder with skip connections for feature reconstruction."""

    def __init__(self, encoder_dims=[96, 192, 384, 768], decoder_dims=[384, 192, 96, 48]):
        super().__init__()

        # Upsampling layers
        self.upsamples = nn.ModuleList()
        self.decoder_blocks = nn.ModuleList()

        # From bottleneck (768) to decoder_dims[0] (384)
        self.upsamples.append(
            nn.ConvTranspose2d(encoder_dims[3], decoder_dims[0], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[0] + encoder_dims[2], decoder_dims[0], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[0], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[0])
            )
        )

        # From decoder_dims[0] (384) to decoder_dims[1] (192)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[0], decoder_dims[1], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[1] + encoder_dims[1], decoder_dims[1], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[1], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[1])
            )
        )

        # From decoder_dims[1] (192) to decoder_dims[2] (96)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[1], decoder_dims[2], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[2] + encoder_dims[0], decoder_dims[2], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[2], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[2])
            )
        )

        # Final upsampling to original resolution
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[2], decoder_dims[3], kernel_size=4, stride=4)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[3], decoder_dims[3], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[3], data_format="channels_first"),
                nn.GELU()
            )
        )

    def forward(self, encoder_features):
        """
        Args:
            encoder_features: List of features from encoder [feat0, feat1, feat2, feat3]
                             where feat3 is the bottleneck feature
        Returns:
            Decoded feature map at original resolution
        """
        x = encoder_features[3]  # Start from bottleneck

        # Decode with skip connections
        for i in range(4):
            x = self.upsamples[i](x)
            if i < 3:  # Skip connection for first 3 layers
                skip_feat = encoder_features[2-i]  # Reverse order: feat2, feat1, feat0
                x = torch.cat([x, skip_feat], dim=1)
            x = self.decoder_blocks[i](x)

        return x


# -----------------------------------------------------------------------------
# Vector to Skew-Symmetric Matrix Conversion
# -----------------------------------------------------------------------------
def vector_to_skew_symmetric(vec, matrix_dim):
    """Convert vector to skew-symmetric matrix.
    Args:
        vec: (batch_size, D), where D = M*(M-1)/2
        matrix_dim: M
    Returns:
        skew_matrix: (batch_size, M, M) skew-symmetric matrix
    """
    batch_size = vec.shape[0]
    device = vec.device
    skew_matrix = torch.zeros(batch_size, matrix_dim, matrix_dim, device=device, dtype=vec.dtype)

    # Get upper triangular indices
    triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

    # Populate the upper triangle
    skew_matrix[:, triu_indices[0], triu_indices[1]] = vec

    # Create the skew-symmetric matrix: A - A^T
    return skew_matrix - skew_matrix.transpose(-2, -1)


# -----------------------------------------------------------------------------
# Enhanced Scaled Cayley Transform Module
# -----------------------------------------------------------------------------
class ScaledCayleyTransform(nn.Module):
    """Enhanced Scaled Cayley Transform for generating orthogonal matrices with numerical stability.

    Implements the scaled Cayley transform as described in Section 3.2:
    W = (I - A) @ inv(I + A) @ D

    Key enhancements:
    - Double precision computation for critical operations (Section 3.4)
    - Regularization for numerical stability
    - Support for learnable D matrix
    - Comprehensive orthogonality checking
    """

    def __init__(self, matrix_dim, num_neg_ones_in_D=0, eps=1e-8, learnable_D=False,
                 use_double_precision=True, regularization_strength=1e-8):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.eps = eps
        self.learnable_D = learnable_D
        self.use_double_precision = use_double_precision
        self.regularization_strength = regularization_strength

        if learnable_D:
            # Learnable D matrix: parameterize as sigmoid-gated signs
            # This allows the network to learn optimal D configuration
            self.D_logits = nn.Parameter(torch.randn(matrix_dim))
            print(f"[Enhanced ScaledCayleyTransform] Using learnable D matrix with {matrix_dim} parameters")
        else:
            # Fixed D matrix as a non-trainable buffer (original implementation)
            D = torch.ones(matrix_dim)
            if num_neg_ones_in_D > 0:
                D[:num_neg_ones_in_D] = -1.0
            self.register_buffer('D', torch.diag(D))
            print(f"[Enhanced ScaledCayleyTransform] Using fixed D matrix with {num_neg_ones_in_D} negative ones")

        print(f"[Enhanced ScaledCayleyTransform] Double precision: {use_double_precision}, "
              f"Regularization: {regularization_strength}")

    def get_D_matrix(self):
        """Get the current D matrix (either fixed or learned)."""
        if self.learnable_D:
            # Convert logits to {-1, +1} via tanh and sign
            D_signs = torch.tanh(self.D_logits)  # Smooth approximation
            # For hard assignment: D_signs = torch.sign(self.D_logits)
            return torch.diag(D_signs)
        else:
            return self.D

    def forward(self, A):
        """Apply Enhanced Scaled Cayley Transform: W = (I - A) @ inv(I + A) @ D

        Enhanced with numerical stability improvements as described in Section 3.4:
        - Double precision computation for critical operations
        - Advanced regularization strategies
        - Robust matrix inversion with multiple fallback methods
        - Comprehensive error handling

        Args:
            A: skew-symmetric/skew-Hermitian matrix (batch_size, M, M)
        Returns:
            W: orthogonal/unitary matrix (batch_size, M, M)
        """
        batch_size = A.shape[0]
        device = A.device
        dtype = A.dtype
        is_complex = torch.is_complex(A)

        # Create identity matrix
        if is_complex:
            Id = torch.eye(self.matrix_dim, device=device, dtype=dtype).expand(batch_size, -1, -1)
        else:
            Id = torch.eye(self.matrix_dim, device=device, dtype=dtype).expand(batch_size, -1, -1)

        # Enhanced numerical stability with double precision (Section 3.4)
        if self.use_double_precision and dtype in [torch.float32, torch.complex64]:
            target_dtype = torch.float64 if not is_complex else torch.complex128
            A_high_prec = A.to(target_dtype)
            Id_high_prec = Id.to(target_dtype)

            # Compute (I + A) and (I - A) in high precision
            I_plus_A = Id_high_prec + A_high_prec
            I_minus_A = Id_high_prec - A_high_prec

            # Advanced regularization strategy
            reg_strength = max(self.regularization_strength, self.eps)
            if is_complex:
                reg_term = reg_strength * torch.eye(self.matrix_dim, device=device, dtype=target_dtype)
            else:
                reg_term = reg_strength * torch.eye(self.matrix_dim, device=device, dtype=target_dtype)

            # Apply regularization to improve conditioning
            I_plus_A_reg = I_plus_A + reg_term.expand_as(I_plus_A)

            # Robust matrix inversion with multiple fallback strategies
            Q = self._robust_matrix_solve(I_plus_A_reg, I_minus_A)

            # Convert back to original precision
            Q = Q.to(dtype)
        else:
            # Direct computation in original precision
            I_plus_A = Id + A
            I_minus_A = Id - A

            # Apply regularization
            reg_term = self.regularization_strength * torch.eye(self.matrix_dim, device=device, dtype=dtype)
            I_plus_A_reg = I_plus_A + reg_term.expand_as(I_plus_A)

            Q = self._robust_matrix_solve(I_plus_A_reg, I_minus_A)

        # Apply the scaling matrix D (either fixed or learned)
        D_matrix = self.get_D_matrix().to(dtype)
        W = torch.matmul(Q, D_matrix)

        return W

    def _robust_matrix_solve(self, A, B):
        """Robust matrix solve with multiple fallback strategies."""
        try:
            # Primary method: direct solve
            return torch.linalg.solve(A, B)
        except RuntimeError as e:
            print(f"[ScaledCayleyTransform] Direct solve failed: {e}, trying LU decomposition...")
            try:
                # Fallback 1: LU decomposition
                LU, pivots = torch.linalg.lu_factor(A)
                return torch.linalg.lu_solve(LU, pivots, B)
            except RuntimeError as e2:
                print(f"[ScaledCayleyTransform] LU solve failed: {e2}, using pseudo-inverse...")
                # Fallback 2: pseudo-inverse (most robust but potentially less accurate)
                return torch.matmul(torch.linalg.pinv(A), B)

    def check_orthogonality(self, W, return_detailed=False):
        """Enhanced orthogonality checking with comprehensive metrics.

        Args:
            W: Output matrix to check
            return_detailed: If True, return detailed orthogonality metrics

        Returns:
            If return_detailed=False: scalar orthogonality error
            If return_detailed=True: dict with detailed metrics
        """
        is_complex = torch.is_complex(W)

        if is_complex:
            # For complex matrices: W^H @ W should equal I
            WH = torch.conj(W).transpose(-2, -1)
            product = torch.matmul(WH, W)
        else:
            # For real matrices: W^T @ W should equal I
            product = torch.matmul(W.transpose(-2, -1), W)

        # Create identity matrix
        I = torch.eye(W.size(-1), device=W.device, dtype=W.dtype)
        if W.ndim == 3:  # Batch dimension
            I = I.expand_as(product)

        # Compute various orthogonality metrics
        diff = product - I

        # Frobenius norm error (primary metric)
        frobenius_error = torch.mean(torch.abs(diff) ** 2)

        if not return_detailed:
            return frobenius_error.item()

        # Detailed metrics for debugging
        max_error = torch.max(torch.abs(diff))
        spectral_error = torch.max(torch.linalg.svdvals(diff))

        # Condition number of the matrix
        try:
            cond_number = torch.linalg.cond(W)
            cond_number = torch.mean(cond_number) if W.ndim == 3 else cond_number
        except:
            cond_number = torch.tensor(float('inf'))

        return {
            'frobenius_error': frobenius_error.item(),
            'max_error': max_error.item(),
            'spectral_error': spectral_error.item(),
            'condition_number': cond_number.item(),
            'is_well_conditioned': cond_number.item() < 1e12
        }


# -----------------------------------------------------------------------------
# Enhanced Unitary Matrix Generator
# -----------------------------------------------------------------------------
class UnitaryGenerator(nn.Module):
    """Enhanced module that converts parameter vectors to unitary matrices with complex support.

    Implements the complete pipeline described in Section 3:
    1. Parameter vector to skew-Hermitian/skew-symmetric matrix conversion
    2. Enhanced Scaled Cayley Transform with numerical stability
    3. Truncation to desired rank with proper manifold projection
    """

    def __init__(self, matrix_dim, rank, num_neg_ones_in_D=0, complex_matrices=True,
                 learnable_D=False, use_double_precision=True):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.rank = rank
        self.complex_matrices = complex_matrices
        self.learnable_D = learnable_D
        self.use_double_precision = use_double_precision

        if complex_matrices:
            # For complex matrices, we need parameters for both real and imaginary parts
            # of the skew-Hermitian matrix: A^H = -A
            # This requires matrix_dim^2 real parameters for a full skew-Hermitian matrix
            self.param_dim = matrix_dim * matrix_dim
        else:
            # For real matrices, skew-symmetric: A^T = -A
            self.param_dim = matrix_dim * (matrix_dim - 1) // 2

        # Enhanced Cayley transform with configurable D matrix and numerical stability
        self.cayley_transform = ScaledCayleyTransform(
            matrix_dim,
            num_neg_ones_in_D=num_neg_ones_in_D,
            learnable_D=learnable_D,
            use_double_precision=use_double_precision,
            regularization_strength=1e-8
        )

        print(f"[Enhanced UnitaryGenerator] Matrix dim: {matrix_dim}, Rank: {rank}, "
              f"Complex: {complex_matrices}, Param dim: {self.param_dim}")

    def forward(self, param_vec):
        """Convert parameter vector to unitary matrix with enhanced validation.

        Args:
            param_vec: (batch_size, param_dim) parameter vector
        Returns:
            unitary_mat: (batch_size, matrix_dim, rank, 2) truncated unitary matrix
        """
        batch_size = param_vec.shape[0]

        # Validate input dimensions
        if param_vec.shape[1] != self.param_dim:
            raise ValueError(f"Expected param_vec with {self.param_dim} parameters, "
                           f"got {param_vec.shape[1]}")

        if self.complex_matrices:
            # Convert vector to skew-Hermitian matrix for complex case
            A = self._vector_to_skew_hermitian(param_vec, self.matrix_dim)
        else:
            # Convert vector to skew-symmetric matrix for real case
            A = vector_to_skew_symmetric(param_vec, self.matrix_dim)

        # Validate skew-Hermitian/skew-symmetric property
        if self.training and torch.rand(1).item() < 0.01:  # Occasional validation during training
            self._validate_skew_property(A)

        # Apply Scaled Cayley Transform to get unitary/orthogonal matrix
        W = self.cayley_transform(A)  # (batch_size, matrix_dim, matrix_dim)

        # Validate orthogonality (occasional check during training)
        if self.training and torch.rand(1).item() < 0.01:
            orth_error = self.cayley_transform.check_orthogonality(W)
            if orth_error > 1e-4:
                print(f"[UnitaryGenerator] Warning: High orthogonality error: {orth_error:.2e}")

        # Truncate to get the first R columns
        W_truncated = W[:, :, :self.rank]  # (batch_size, matrix_dim, rank)

        if self.complex_matrices and torch.is_complex(W_truncated):
            # Convert complex tensor to real-imaginary format
            W_ri = torch.stack([W_truncated.real, W_truncated.imag], dim=-1)
        else:
            # For real matrices, imaginary part is zero
            W_ri = torch.stack([W_truncated, torch.zeros_like(W_truncated)], dim=-1)

        return W_ri

    def _validate_skew_property(self, A):
        """Validate that A satisfies skew-Hermitian or skew-symmetric property."""
        if torch.is_complex(A):
            # Check A^H = -A
            A_H = torch.conj(A).transpose(-2, -1)
            error = torch.mean(torch.abs(A_H + A) ** 2)
        else:
            # Check A^T = -A
            error = torch.mean(torch.abs(A.transpose(-2, -1) + A) ** 2)

        if error > 1e-6:
            print(f"[UnitaryGenerator] Warning: Skew property violation: {error:.2e}")

    def _vector_to_skew_hermitian(self, vec, matrix_dim):
        """Convert vector to skew-Hermitian matrix A where A^H = -A.

        Args:
            vec: (batch_size, matrix_dim^2) parameter vector
            matrix_dim: dimension of the output matrix
        Returns:
            skew_hermitian: (batch_size, matrix_dim, matrix_dim) skew-Hermitian matrix
        """
        batch_size = vec.shape[0]
        device = vec.device
        dtype = torch.complex64 if vec.dtype == torch.float32 else torch.complex128

        # Reshape vector to matrix form
        vec_reshaped = vec.view(batch_size, matrix_dim, matrix_dim)

        # Create skew-Hermitian matrix
        # Diagonal elements are purely imaginary
        diag_imag = vec_reshaped[:, range(matrix_dim), range(matrix_dim)]

        # Off-diagonal elements: upper triangle defines the matrix
        triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

        # Create complex matrix
        A = torch.zeros(batch_size, matrix_dim, matrix_dim, dtype=dtype, device=device)

        # Set diagonal to purely imaginary
        A[:, range(matrix_dim), range(matrix_dim)] = 1j * diag_imag

        # Set upper triangle
        upper_real = vec_reshaped[:, triu_indices[0], triu_indices[1]]
        upper_imag = torch.zeros_like(upper_real)  # Can be extended to include imaginary parts
        A[:, triu_indices[0], triu_indices[1]] = upper_real + 1j * upper_imag

        # Set lower triangle as conjugate transpose of upper triangle
        A[:, triu_indices[1], triu_indices[0]] = -torch.conj(A[:, triu_indices[0], triu_indices[1]])

        return A


# -----------------------------------------------------------------------------
# Enhanced SVDNet with ConvNeXt-UNet Architecture
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """Enhanced SVDNet with ConvNeXt-UNet backbone and geometric-aware design.

    Architecture follows the optimization plan:
    - Lightweight ConvNeXt-UNet encoder-decoder with skip connections
    - Multi-task prediction heads for U, V, and S
    - Enhanced Scaled Cayley Transform for hard orthogonality constraints
    - Support for complex matrices and numerical stability
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth",
                 num_neg_ones_in_D: int = 0, learnable_D: bool = False):
        super().__init__()
        self.dim = dim  # Antenna dimension (M == N)
        self.rank = rank

        # --------------------------- CS-UNet Hybrid Backbone ----------------------------
        # ConvNeXt-Swin-UNet hybrid encoder as described in Section 2.3
        encoder_dims = [96, 192, 384, 768]  # ConvNeXt-T dimensions
        decoder_dims = [384, 192, 96, 48]   # Decoder dimensions

        self.encoder = CSUNetEncoder(
            in_chans=2,
            depths=[3, 3, 9, 3],  # ConvNeXt-T depths for first 3 stages
            dims=encoder_dims,
            drop_path_rate=0.1,
            layer_scale_init_value=1e-6,
            swin_num_heads=24,  # Number of attention heads for Swin bottleneck
            swin_window_size=7,  # Window size for Swin attention
            swin_depth=2  # Number of Swin Transformer blocks in bottleneck
        )

        # ConvNeXt-UNet decoder with skip connections
        self.decoder = ConvNeXtUNetDecoder(
            encoder_dims=encoder_dims,
            decoder_dims=decoder_dims
        )

        # ------------------------ Multi-task Prediction Heads ----------------------------
        # Shared feature dimension from decoder
        shared_dim = decoder_dims[3]  # 48

        # U prediction head - generates parameters for unitary matrix U
        self.head_U = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim),  # Parameters for complex skew-Hermitian matrix
        )

        # V prediction head - generates parameters for unitary matrix V
        self.head_V = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim),  # Parameters for complex skew-Hermitian matrix
        )

        # S prediction head - generates singular values
        self.head_S = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.GELU(),
            nn.Linear(128, rank),
            nn.Softplus()  # Ensure non-negative singular values
        )

        # ----------------------- Enhanced Unitary Generators ------------------------
        # Use enhanced generators with complex matrix support and configurable D matrix
        self.generator_U = UnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            complex_matrices=True,
            learnable_D=learnable_D
        )
        self.generator_V = UnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            complex_matrices=True,
            learnable_D=learnable_D
        )

        # ------------------------ Weight loading ----------------------------
        if os.path.isfile(weight_path):
            state = torch.load(weight_path, map_location="cpu")
            try:
                self.load_state_dict(state, strict=False)
                print(f"[Enhanced SVDNet] Loaded weights from {weight_path} (strict=False)")
            except RuntimeError as e:
                print(f"[Enhanced SVDNet] Weight loading failed: {e}. Proceeding with random init.")

    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor):
        """Enhanced forward pass with CS-UNet hybrid backbone.

        Architecture flow:
        1. ConvNeXt encoder stages (high/medium resolution) - efficient local feature extraction
        2. Swin Transformer bottleneck (low resolution) - global context modeling
        3. ConvNeXt-UNet decoder with skip connections - feature reconstruction
        4. Multi-task heads with Enhanced Scaled Cayley Transform - SVD component generation

        Args:
            x: complex channel, shape [M, N, 2] or [B, M, N, 2]
        Returns:
            U: left unitary matrix [B, M, R, 2] or [M, R, 2]
            S: singular values [B, R] or [R]
            V: right unitary matrix [B, N, R, 2] or [N, R, 2]
        """
        if x.ndim == 3:
            x = x.unsqueeze(0)
        if x.shape[-1] != 2:
            raise ValueError("Input last dim must be 2 (real/imag)")
        B = x.size(0)

        # Reorder to NCHW format for ConvNeXt processing
        feat = x.permute(0, 3, 1, 2)  # [B, 2, M, N]

        # CS-UNet encoder: ConvNeXt stages + Swin Transformer bottleneck
        encoder_features = self.encoder(feat)  # List of features at different scales

        # ConvNeXt-UNet decoder: reconstruct features with skip connections
        decoded_feat = self.decoder(encoder_features)  # [B, 48, M, N]

        # Multi-task prediction heads
        # U head: predict parameters for unitary matrix U
        U_params = self.head_U(decoded_feat)  # [B, dim*dim]

        # V head: predict parameters for unitary matrix V
        V_params = self.head_V(decoded_feat)  # [B, dim*dim]

        # S head: predict singular values
        S = self.head_S(decoded_feat)  # [B, rank]

        # Generate unitary matrices using Enhanced Scaled Cayley Transform
        U = self.generator_U(U_params)  # [B, dim, rank, 2]
        V = self.generator_V(V_params)  # [B, dim, rank, 2]

        # Remove batch dim if B==1 (to match demo expectations)
        if B == 1:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V

    # ------------------------------------------------------------------
    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def get_model_complexity(self):
        """Calculate model complexity in terms of parameters and FLOPs."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }