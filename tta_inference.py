#!/usr/bin/env python3
"""
Test-Time Augmentation (TTA) for SVDNet with Manifold-Aware Averaging

This module implements the TTA strategy described in the optimization plan:
- Geometric median computation for Stiefel manifold data
- Manifold-aware averaging for unitary matrices
- Ensemble prediction from multiple snapshots
"""

import torch
import numpy as np
import os
import glob
from solution import SVDNet
from typing import List, Tuple, Dict, Any


def to_complex(t):
    """Convert real-imaginary tensor to complex."""
    return torch.complex(t[..., 0], t[..., 1])


def to_ri(t):
    """Convert complex tensor to real-imaginary."""
    return torch.stack([t.real, t.imag], dim=-1)


class TTATransforms:
    """Test-Time Augmentation transforms for channel matrices."""
    
    @staticmethod
    def add_gaussian_noise(H, noise_std=0.005):
        """Add small Gaussian noise."""
        H_c = to_complex(H)
        noise = torch.randn_like(H_c) * noise_std
        H_aug = H_c + noise
        return to_ri(H_aug)
    
    @staticmethod
    def conjugate_transpose(H):
        """Apply conjugate transpose."""
        H_c = to_complex(H)
        H_aug = torch.conj(H_c.transpose(-2, -1))
        return to_ri(H_aug)
    
    @staticmethod
    def phase_rotation(H, max_angle=0.1):
        """Apply small random phase rotation."""
        H_c = to_complex(H)
        angle = (torch.rand(1, device=H.device) - 0.5) * 2 * max_angle
        rotation = torch.exp(1j * angle)
        H_aug = H_c * rotation
        return to_ri(H_aug)
    
    @staticmethod
    def get_all_transforms():
        """Get all available transforms."""
        return [
            TTATransforms.add_gaussian_noise,
            lambda H: TTATransforms.phase_rotation(H, 0.05),
            lambda H: TTATransforms.phase_rotation(H, -0.05),
            TTATransforms.conjugate_transpose,
        ]


class GeometricMedian:
    """Enhanced geometric median computation on Stiefel manifold with Riemannian metrics."""

    @staticmethod
    def weiszfeld_algorithm(matrices, max_iter=50, tol=1e-6, use_riemannian_distance=False):
        """
        Compute geometric median of unitary matrices using enhanced Weiszfeld algorithm.

        Args:
            matrices: List of complex matrices [U1, U2, ..., UK]
            max_iter: Maximum iterations
            tol: Convergence tolerance
            use_riemannian_distance: If True, use Riemannian distance on Stiefel manifold

        Returns:
            Geometric median matrix
        """
        if len(matrices) == 1:
            return matrices[0]

        # Initialize with arithmetic mean (projected to manifold)
        mean_matrix = torch.mean(torch.stack(matrices), dim=0)
        current = GeometricMedian.project_to_stiefel(mean_matrix)

        for iteration in range(max_iter):
            # Compute distances and weights
            distances = []
            for mat in matrices:
                if use_riemannian_distance:
                    dist = GeometricMedian.riemannian_distance(current, mat)
                else:
                    # Standard Frobenius distance (faster, usually sufficient)
                    dist = torch.norm(current - mat, p='fro')
                distances.append(dist)

            distances = torch.stack(distances)

            # Avoid division by zero
            weights = 1.0 / (distances + 1e-8)
            weights = weights / torch.sum(weights)

            # Weighted average
            weighted_sum = torch.zeros_like(current)
            for i, mat in enumerate(matrices):
                weighted_sum += weights[i] * mat

            # Project back to Stiefel manifold
            new_current = GeometricMedian.project_to_stiefel(weighted_sum)

            # Check convergence
            if torch.norm(new_current - current, p='fro') < tol:
                break

            current = new_current

        return current

    @staticmethod
    def riemannian_distance(U1, U2):
        """
        Compute Riemannian distance on Stiefel manifold.

        For unitary matrices U1, U2, the Riemannian distance is:
        d(U1, U2) = ||log(U1^H @ U2)||_F

        Args:
            U1, U2: Complex unitary matrices

        Returns:
            Riemannian distance
        """
        # Compute U1^H @ U2
        U1_H = torch.conj(U1).transpose(-2, -1)
        relative_rotation = torch.matmul(U1_H, U2)

        # Compute matrix logarithm
        # For unitary matrices, log(U) can be computed via eigendecomposition
        try:
            eigenvals, eigenvecs = torch.linalg.eig(relative_rotation)
            # log(eigenvals) for complex eigenvalues
            log_eigenvals = torch.log(eigenvals + 1e-12)  # Add small epsilon for stability
            log_matrix = torch.matmul(
                torch.matmul(eigenvecs, torch.diag_embed(log_eigenvals)),
                torch.conj(eigenvecs).transpose(-2, -1)
            )
            distance = torch.norm(log_matrix, p='fro')
        except RuntimeError:
            # Fallback to Frobenius distance if eigendecomposition fails
            distance = torch.norm(U1 - U2, p='fro')

        return distance
    
    @staticmethod
    def project_to_stiefel(matrix):
        """Project matrix to Stiefel manifold using SVD."""
        U, _, Vh = torch.linalg.svd(matrix, full_matrices=False)
        return torch.matmul(U, Vh)


class SnapshotEnsembleTTA:
    """Test-Time Augmentation with Snapshot Ensemble."""
    
    def __init__(self, snapshot_dir="snapshots", device="cpu"):
        self.snapshot_dir = snapshot_dir
        self.device = device
        self.models = []
        self.load_snapshots()
    
    def load_snapshots(self):
        """Load all snapshot models."""
        snapshot_files = glob.glob(os.path.join(self.snapshot_dir, "snapshot_*.pth"))
        
        if not snapshot_files:
            print(f"Warning: No snapshots found in {self.snapshot_dir}")
            return
        
        print(f"Loading {len(snapshot_files)} snapshots...")
        
        for snapshot_file in snapshot_files:
            # Load snapshot data
            snapshot_data = torch.load(snapshot_file, map_location=self.device)
            
            # Create model and load state dict
            # Note: We need to know the model dimensions, assuming 64x64 with rank 32
            model = SVDNet(dim=64, rank=32, weight_path="")
            model.load_state_dict(snapshot_data['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            self.models.append({
                'model': model,
                'epoch': snapshot_data['epoch'],
                'loss': snapshot_data['loss']
            })
        
        print(f"Loaded {len(self.models)} snapshot models")
    
    def predict_with_tta(self, H_input, num_augmentations=4, use_riemannian_distance=False):
        """
        Predict with Test-Time Augmentation and Snapshot Ensemble.

        Args:
            H_input: Input channel matrix [M, N, 2] or [B, M, N, 2]
            num_augmentations: Number of augmentations per model
            use_riemannian_distance: Use Riemannian distance for geometric median

        Returns:
            U, S, V: Ensemble predictions
        """
        if len(self.models) == 0:
            raise ValueError("No snapshot models loaded")
        
        # Ensure batch dimension
        if H_input.ndim == 3:
            H_input = H_input.unsqueeze(0)
            squeeze_output = True
        else:
            squeeze_output = False
        
        H_input = H_input.to(self.device)
        
        # Get TTA transforms
        transforms = TTATransforms.get_all_transforms()[:num_augmentations]
        
        all_U_predictions = []
        all_V_predictions = []
        all_S_predictions = []
        
        # For each snapshot model
        for model_info in self.models:
            model = model_info['model']
            
            model_U_preds = []
            model_V_preds = []
            model_S_preds = []
            
            # Apply TTA transforms
            for transform in transforms:
                # Apply transform
                H_aug = transform(H_input)
                
                # Predict
                with torch.no_grad():
                    U_pred, S_pred, V_pred = model(H_aug)
                
                # Ensure batch dimension for processing
                if U_pred.ndim == 3:
                    U_pred = U_pred.unsqueeze(0)
                    S_pred = S_pred.unsqueeze(0)
                    V_pred = V_pred.unsqueeze(0)
                
                # Handle conjugate transpose inverse transform
                if transform == TTATransforms.conjugate_transpose:
                    # Swap and conjugate U and V
                    U_pred, V_pred = V_pred, U_pred
                    U_c = to_complex(U_pred)
                    V_c = to_complex(V_pred)
                    U_pred = to_ri(torch.conj(U_c))
                    V_pred = to_ri(torch.conj(V_c))
                
                model_U_preds.append(to_complex(U_pred))
                model_V_preds.append(to_complex(V_pred))
                model_S_preds.append(S_pred)
            
            # Aggregate predictions for this model using manifold-aware averaging
            if len(model_U_preds) > 1:
                # Geometric median for U and V (manifold data) with optional Riemannian distance
                U_agg = GeometricMedian.weiszfeld_algorithm(
                    model_U_preds, use_riemannian_distance=use_riemannian_distance
                )
                V_agg = GeometricMedian.weiszfeld_algorithm(
                    model_V_preds, use_riemannian_distance=use_riemannian_distance
                )

                # Arithmetic mean for S (Euclidean data)
                S_agg = torch.mean(torch.stack(model_S_preds), dim=0)
            else:
                U_agg = model_U_preds[0]
                V_agg = model_V_preds[0]
                S_agg = model_S_preds[0]
            
            all_U_predictions.append(U_agg)
            all_V_predictions.append(V_agg)
            all_S_predictions.append(S_agg)
        
        # Final ensemble aggregation
        if len(all_U_predictions) > 1:
            # Geometric median for final U and V with optional Riemannian distance
            final_U = GeometricMedian.weiszfeld_algorithm(
                all_U_predictions, use_riemannian_distance=use_riemannian_distance
            )
            final_V = GeometricMedian.weiszfeld_algorithm(
                all_V_predictions, use_riemannian_distance=use_riemannian_distance
            )

            # Arithmetic mean for final S
            final_S = torch.mean(torch.stack(all_S_predictions), dim=0)
        else:
            final_U = all_U_predictions[0]
            final_V = all_V_predictions[0]
            final_S = all_S_predictions[0]
        
        # Convert back to real-imaginary format
        final_U_ri = to_ri(final_U)
        final_V_ri = to_ri(final_V)
        
        # Remove batch dimension if needed
        if squeeze_output:
            final_U_ri = final_U_ri.squeeze(0)
            final_V_ri = final_V_ri.squeeze(0)
            final_S = final_S.squeeze(0)
        
        return final_U_ri, final_S, final_V_ri


def create_tta_predictor(snapshot_dir="snapshots", device="cpu"):
    """Create a TTA predictor instance."""
    return SnapshotEnsembleTTA(snapshot_dir=snapshot_dir, device=device)


if __name__ == "__main__":
    # Example usage
    device = "cuda" if torch.cuda.is_available() else "cpu"
    tta_predictor = create_tta_predictor(device=device)
    
    # Example prediction
    H_test = torch.randn(64, 64, 2)
    U, S, V = tta_predictor.predict_with_tta(H_test, num_augmentations=4)
    
    print(f"TTA Prediction shapes:")
    print(f"U: {U.shape}, S: {S.shape}, V: {V.shape}")
