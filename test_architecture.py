#!/usr/bin/env python3
"""
Test script to verify the Enhanced SVDNet architecture with ConvNeXt-UNet backbone
matches the specifications in the optimization plan.
"""

import torch
import numpy as np
from solution import SVDNet
from loss_functions import AEAlignedLoss, compute_ae_metric
from tta_inference import create_tta_predictor

def test_enhanced_architecture():
    """Test the enhanced architecture with ConvNeXt-UNet backbone."""
    print("Testing Enhanced SVDNet with ConvNeXt-UNet Architecture")
    print("=" * 60)
    
    # Test parameters
    test_cases = [
        {"M": 64, "N": 64, "R": 32, "batch_size": 1},
        {"M": 64, "N": 64, "R": 32, "batch_size": 4},
        {"M": 32, "N": 32, "R": 16, "batch_size": 2},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\nTest Case {i+1}: M={case['M']}, N={case['N']}, R={case['R']}, B={case['batch_size']}")
        print("-" * 40)
        
        # Create model
        model = SVDNet(dim=case['M'], rank=case['R'], weight_path="nonexistent.pth")
        model.eval()
        
        # Create test input
        H_input = torch.randn(case['batch_size'], case['M'], case['N'], 2)
        print(f"Input shape: {H_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            U, S, V = model(H_input)
        
        # Check output shapes
        expected_U_shape = (case['batch_size'], case['M'], case['R'], 2) if case['batch_size'] > 1 else (case['M'], case['R'], 2)
        expected_S_shape = (case['batch_size'], case['R']) if case['batch_size'] > 1 else (case['R'],)
        expected_V_shape = (case['batch_size'], case['N'], case['R'], 2) if case['batch_size'] > 1 else (case['N'], case['R'], 2)
        
        print(f"U shape: {U.shape} (expected: {expected_U_shape})")
        print(f"S shape: {S.shape} (expected: {expected_S_shape})")
        print(f"V shape: {V.shape} (expected: {expected_V_shape})")
        
        # Verify shapes
        assert U.shape == expected_U_shape, f"U shape mismatch: {U.shape} vs {expected_U_shape}"
        assert S.shape == expected_S_shape, f"S shape mismatch: {S.shape} vs {expected_S_shape}"
        assert V.shape == expected_V_shape, f"V shape mismatch: {V.shape} vs {expected_V_shape}"
        
        # Test orthogonality (should be near zero due to Cayley transform)
        if case['batch_size'] == 1:
            U_batch = U.unsqueeze(0)
            V_batch = V.unsqueeze(0)
        else:
            U_batch = U
            V_batch = V
            
        U_complex = torch.complex(U_batch[..., 0], U_batch[..., 1])
        V_complex = torch.complex(V_batch[..., 0], V_batch[..., 1])
        
        # Check U^H @ U = I
        U_H = torch.conj(U_complex).transpose(-2, -1)
        UHU = torch.matmul(U_H, U_complex)
        I_U = torch.eye(case['R'], dtype=UHU.dtype, device=UHU.device)
        orth_error_U = torch.mean(torch.abs(UHU - I_U) ** 2).item()
        
        # Check V^H @ V = I
        V_H = torch.conj(V_complex).transpose(-2, -1)
        VHV = torch.matmul(V_H, V_complex)
        I_V = torch.eye(case['R'], dtype=VHV.dtype, device=VHV.device)
        orth_error_V = torch.mean(torch.abs(VHV - I_V) ** 2).item()
        
        print(f"Orthogonality error U: {orth_error_U:.2e}")
        print(f"Orthogonality error V: {orth_error_V:.2e}")
        
        # Check singular values are non-negative
        S_min = torch.min(S if case['batch_size'] > 1 else S).item()
        print(f"Minimum singular value: {S_min:.6f}")
        assert S_min >= 0, f"Negative singular value detected: {S_min}"
        
        # Test AE metric computation
        ae_value = compute_ae_metric(U, S, V, H_input)
        print(f"AE metric: {ae_value.item():.6f}")

        print("✓ Test passed!")

    print("\n" + "=" * 60)
    print("All enhanced architecture tests passed successfully!")
    print("The implementation follows the optimization plan specifications.")

def test_model_parameters():
    """Test model parameter count for efficiency."""
    print("\nModel Parameter Analysis")
    print("=" * 30)
    
    model = SVDNet(dim=64, rank=32, weight_path="nonexistent.pth")
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Analyze by module
    print("\nParameter breakdown by module:")
    for name, module in model.named_children():
        module_params = sum(p.numel() for p in module.parameters())
        print(f"  {name}: {module_params:,} parameters")

def test_loss_functions():
    """Test the AE-aligned loss functions."""
    print("\nTesting AE-Aligned Loss Functions")
    print("=" * 40)

    device = "cuda" if torch.cuda.is_available() else "cpu"

    # Create test data
    B, M, N, R = 2, 64, 64, 32
    U = torch.randn(B, M, R, 2, device=device)
    S = torch.rand(B, R, device=device)
    V = torch.randn(B, N, R, 2, device=device)
    H_gt = torch.randn(B, M, N, 2, device=device)

    # Test loss function
    loss_fn = AEAlignedLoss().to(device)
    loss_dict = loss_fn(U, S, V, H_gt)

    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")

    # Test AE metric
    ae_value = compute_ae_metric(U, S, V, H_gt)
    print(f"\nDirect AE metric: {ae_value.item():.6f}")
    print("✓ Loss function tests passed!")


def test_enhanced_features():
    """Test the enhanced features: learnable D matrix, Riemannian distance, adaptive loss."""
    print("\nTesting Enhanced Features")
    print("=" * 35)

    device = "cuda" if torch.cuda.is_available() else "cpu"

    # Test 1: Learnable D matrix
    print("1. Testing learnable D matrix...")
    model_learnable = SVDNet(dim=32, rank=16, learnable_D=True, weight_path="")
    model_fixed = SVDNet(dim=32, rank=16, learnable_D=False, weight_path="")

    learnable_params = sum(p.numel() for p in model_learnable.parameters())
    fixed_params = sum(p.numel() for p in model_fixed.parameters())

    print(f"  Fixed D model parameters: {fixed_params}")
    print(f"  Learnable D model parameters: {learnable_params}")
    print(f"  Additional parameters: {learnable_params - fixed_params}")

    # Test forward pass
    H_test = torch.randn(2, 32, 32, 2, device=device)
    model_learnable.to(device)

    U, S, V = model_learnable(H_test)
    print(f"  Output shapes: U{U.shape}, S{S.shape}, V{V.shape}")
    print("  ✓ Learnable D matrix test passed!")

    # Test 2: Adaptive loss function
    print("\n2. Testing adaptive loss function...")
    from loss_functions import AdaptiveLoss

    adaptive_loss = AdaptiveLoss().to(device)
    loss_dict = adaptive_loss(U, S, V, H_test)

    print(f"  Initial orthogonality weights:")
    print(f"    lambda_orth_u: {torch.exp(adaptive_loss.log_lambda_orth_u).item():.4f}")
    print(f"    lambda_orth_v: {torch.exp(adaptive_loss.log_lambda_orth_v).item():.4f}")
    print(f"  Loss components: {list(loss_dict.keys())}")
    print("  ✓ Adaptive loss function test passed!")

    # Test 3: Riemannian distance (if TTA available)
    print("\n3. Testing Riemannian distance computation...")
    try:
        from tta_inference import GeometricMedian

        # Create test unitary matrices
        U1 = torch.randn(16, 8, dtype=torch.complex64)
        U2 = torch.randn(16, 8, dtype=torch.complex64)

        # Project to Stiefel manifold
        U1 = GeometricMedian.project_to_stiefel(U1)
        U2 = GeometricMedian.project_to_stiefel(U2)

        # Compute distances
        frobenius_dist = torch.norm(U1 - U2, p='fro').item()
        riemannian_dist = GeometricMedian.riemannian_distance(U1, U2).item()

        print(f"  Frobenius distance: {frobenius_dist:.4f}")
        print(f"  Riemannian distance: {riemannian_dist:.4f}")
        print("  ✓ Riemannian distance computation test passed!")

    except Exception as e:
        print(f"  Riemannian distance test failed: {e}")
        print("  This may be expected due to eigendecomposition complexity")


def test_tta_functionality():
    """Test enhanced TTA functionality with Riemannian distance option."""
    print("\nTesting Enhanced TTA Functionality")
    print("=" * 40)

    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        tta_predictor = create_tta_predictor(device=device)

        if len(tta_predictor.models) > 0:
            # Test TTA prediction with both distance metrics
            H_test = torch.randn(64, 64, 2)

            print("Testing with Frobenius distance...")
            U1, S1, V1 = tta_predictor.predict_with_tta(
                H_test, num_augmentations=2, use_riemannian_distance=False
            )

            print("Testing with Riemannian distance...")
            U2, S2, V2 = tta_predictor.predict_with_tta(
                H_test, num_augmentations=2, use_riemannian_distance=True
            )

            print(f"TTA prediction shapes:")
            print(f"  U: {U1.shape}, S: {S1.shape}, V: {V1.shape}")

            # Compare results
            u_diff = torch.norm(U1 - U2).item()
            s_diff = torch.norm(S1 - S2).item()
            v_diff = torch.norm(V1 - V2).item()

            print(f"Difference between distance metrics:")
            print(f"  U difference: {u_diff:.6f}")
            print(f"  S difference: {s_diff:.6f}")
            print(f"  V difference: {v_diff:.6f}")
            print("✓ Enhanced TTA functionality tests passed!")
        else:
            print("No snapshots found - TTA test skipped")

    except Exception as e:
        print(f"TTA test failed: {e}")
        print("This is expected if no snapshots are available")


if __name__ == "__main__":
    test_enhanced_architecture()
    test_model_parameters()
    test_loss_functions()
    test_enhanced_features()
    test_tta_functionality()
